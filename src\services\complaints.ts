/*
 * @Description: 投诉建议管理
 */
import { request } from '@umijs/max';

/** 查询投诉建议列表  GET /admin/complaints */
export async function index(params: {
  current?: number;
  pageSize?: number;
  category?: API.ComplaintCategory;
  subCategory?: API.ComplaintSubCategory;
  status?: API.ComplaintStatus;
  keyword?: string;
  startDate?: string;
  endDate?: string;
  customerId?: number;
  orderId?: number;
  employeeId?: number;
  handlerId?: number;
}) {
  return request<API.ResType<{ total?: number; list?: API.Complaint[] }>>(
    '/admin/complaints',
    {
      method: 'GET',
      params,
    },
  );
}

/** 查询单个投诉建议  GET /admin/complaints/:id */
export async function show(id: number) {
  return request<API.ResType<API.Complaint>>(`/admin/complaints/${id}`, {
    method: 'GET',
  });
}

/** 管理员录入投诉建议  POST /admin/complaints */
export async function create(body: {
  category: API.ComplaintCategory;
  subCategory: API.ComplaintSubCategory;
  title: string;
  content: string;
  customerId?: number;
  orderId?: number;
  employeeId?: number;
  contactInfo?: string;
  photoURLs?: string[];
  createdBy?: number;
  adminNote?: string;
}) {
  return request<API.ResType<API.Complaint>>('/admin/complaints', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
  });
}

/** 管理员更新投诉建议  PUT /admin/complaints/:id */
export async function update(
  id: number,
  body: Partial<{
    category: API.ComplaintCategory;
    subCategory: API.ComplaintSubCategory;
    title: string;
    content: string;
    customerId?: number;
    orderId?: number;
    employeeId?: number;
    contactInfo?: string;
    photoURLs?: string[];
    adminNote?: string;
  }>,
) {
  return request<API.ResType<API.Complaint>>(`/admin/complaints/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
  });
}

/** 删除投诉建议  DELETE /admin/complaints/:id */
export async function remove(id: number) {
  return request<API.ResType<unknown>>(`/admin/complaints/${id}`, {
    method: 'DELETE',
  });
}

/** 处理投诉建议  PATCH /admin/complaints/:id/handle */
export async function handle(
  id: number,
  body: {
    status: API.ComplaintStatus;
    result?: string;
    handlerId: number;
  },
) {
  return request<API.ResType<API.Complaint>>(`/admin/complaints/${id}/handle`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
  });
}

/** 更新投诉状态  PATCH /admin/complaints/:id/status */
export async function updateStatus(
  id: number,
  body: {
    status: API.ComplaintStatus;
  },
) {
  return request<API.ResType<API.Complaint>>(`/admin/complaints/${id}/status`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
  });
}

/** 批量处理投诉建议  POST /admin/complaints/batch-handle */
export async function batchHandle(body: {
  ids: number[];
  handleData: {
    status: API.ComplaintStatus;
    result: string;
    handlerId: number;
  };
}) {
  return request<
    API.ResType<{
      message: string;
      results: Array<{
        id: number;
        success: boolean;
        data?: any;
        error?: string;
      }>;
      successCount: number;
      failCount: number;
    }>
  >('/admin/complaints/batch-handle', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
  });
}

/** 批量删除投诉建议  DELETE /admin/complaints/batch */
export async function batchDelete(body: { ids: number[] }) {
  return request<
    API.ResType<{
      message: string;
      results: Array<{
        id: number;
        success: boolean;
        error?: string;
      }>;
      successCount: number;
      failCount: number;
    }>
  >('/admin/complaints/batch', {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
  });
}

/** 获取统计信息  GET /admin/complaints/statistics/summary */
export async function statistics(params?: {
  startDate?: string;
  endDate?: string;
  category?: API.ComplaintCategory;
  subCategory?: API.ComplaintSubCategory;
}) {
  return request<API.ResType<API.ComplaintStatistics>>(
    '/admin/complaints/statistics/summary',
    {
      method: 'GET',
      params,
    },
  );
}

// ==================== 投诉建议统计接口 ====================

/** 投诉建议概览统计  GET /complaint-statistics/overview */
export async function overview(params?: {
  startDate?: string;
  endDate?: string;
}) {
  return request<API.ResType<API.ComplaintOverviewStats>>(
    '/complaint-statistics/overview',
    {
      method: 'GET',
      params,
    },
  );
}

/** 投诉建议趋势统计  GET /complaint-statistics/trend */
export async function trend(params: {
  startDate: string;
  endDate: string;
  periodType?: 'day' | 'week' | 'month';
}) {
  return request<API.ResType<API.ComplaintTrendStats[]>>(
    '/complaint-statistics/trend',
    {
      method: 'GET',
      params,
    },
  );
}

/** 客户投诉建议统计  GET /complaint-statistics/customer */
export async function customerStats(params?: {
  startDate?: string;
  endDate?: string;
  page?: number;
  pageSize?: number;
  sortBy?: 'complaintCount' | 'suggestionCount' | 'totalCount';
  sortOrder?: 'asc' | 'desc';
}) {
  return request<
    API.ResType<API.PaginatedResponse<API.ComplaintCustomerStats>>
  >('/complaint-statistics/customer', {
    method: 'GET',
    params,
  });
}

/** 员工投诉统计  GET /complaint-statistics/employee */
export async function employeeStats(params?: {
  startDate?: string;
  endDate?: string;
  page?: number;
  pageSize?: number;
  sortBy?: 'complaintCount' | 'resolvedCount' | 'resolveRate';
  sortOrder?: 'asc' | 'desc';
}) {
  return request<
    API.ResType<API.PaginatedResponse<API.ComplaintEmployeeStats>>
  >('/complaint-statistics/employee', {
    method: 'GET',
    params,
  });
}

/** 处理效率统计  GET /complaint-statistics/processing-efficiency */
export async function processingEfficiency(params?: {
  startDate?: string;
  endDate?: string;
}) {
  return request<API.ResType<API.ComplaintProcessingEfficiencyStats>>(
    '/complaint-statistics/processing-efficiency',
    {
      method: 'GET',
      params,
    },
  );
}

/** 热点问题分析  GET /complaint-statistics/hot-issues */
export async function hotIssues(params?: {
  startDate?: string;
  endDate?: string;
  limit?: number;
}) {
  return request<API.ResType<API.ComplaintHotIssuesStats>>(
    '/complaint-statistics/hot-issues',
    {
      method: 'GET',
      params,
    },
  );
}

/** 状态分布统计  GET /complaint-statistics/status-distribution */
export async function statusDistribution(params?: {
  startDate?: string;
  endDate?: string;
  category?: API.ComplaintCategory;
  subCategory?: API.ComplaintSubCategory;
}) {
  return request<API.ResType<API.ComplaintStatusDistributionStats>>(
    '/complaint-statistics/status-distribution',
    {
      method: 'GET',
      params,
    },
  );
}

/** 投诉建议排行榜  GET /complaint-statistics/ranking */
export async function ranking(params?: {
  type?: 'customer' | 'employee';
  startDate?: string;
  endDate?: string;
  limit?: number;
}) {
  return request<API.ResType<API.ComplaintRankingStats>>(
    '/complaint-statistics/ranking',
    {
      method: 'GET',
      params,
    },
  );
}

/** 处理时效统计  GET /complaint-statistics/processing-time */
export async function processingTime(params?: {
  startDate?: string;
  endDate?: string;
}) {
  return request<API.ResType<API.ComplaintProcessingTimeStats>>(
    '/complaint-statistics/processing-time',
    {
      method: 'GET',
      params,
    },
  );
}

/** 解决率统计  GET /complaint-statistics/resolve-rate */
export async function resolveRate(params?: {
  startDate?: string;
  endDate?: string;
  groupBy?: 'category' | 'subCategory' | 'month';
}) {
  return request<API.ResType<API.ComplaintResolveRateStats>>(
    '/complaint-statistics/resolve-rate',
    {
      method: 'GET',
      params,
    },
  );
}

/** 处理人员统计  GET /complaint-statistics/handler */
export async function handlerStats(params?: {
  startDate?: string;
  endDate?: string;
  page?: number;
  pageSize?: number;
  sortBy?: 'handledCount' | 'avgProcessingHours' | 'resolveRate';
  sortOrder?: 'asc' | 'desc';
}) {
  return request<API.ResType<API.PaginatedResponse<API.ComplaintHandlerStats>>>(
    '/complaint-statistics/handler',
    {
      method: 'GET',
      params,
    },
  );
}

/** 时间分布统计  GET /complaint-statistics/time-distribution */
export async function timeDistribution(params?: {
  startDate?: string;
  endDate?: string;
  timeType?: 'hour' | 'weekday' | 'month';
}) {
  return request<API.ResType<API.ComplaintTimeDistributionStats[]>>(
    '/complaint-statistics/time-distribution',
    {
      method: 'GET',
      params,
    },
  );
}

/** 满意度统计  GET /complaint-statistics/satisfaction */
export async function satisfaction(params?: {
  startDate?: string;
  endDate?: string;
  category?: API.ComplaintCategory;
  subCategory?: API.ComplaintSubCategory;
}) {
  return request<API.ResType<API.ComplaintSatisfactionStats>>(
    '/complaint-statistics/satisfaction',
    {
      method: 'GET',
      params,
    },
  );
}

/** 根据订单获取投诉建议  GET /admin/complaints/order/:orderId/complaints */
export async function getByOrder(orderId: number) {
  return request<API.ResType<{ list?: API.Complaint[] }>>(
    `/admin/complaints/order/${orderId}/complaints`,
    {
      method: 'GET',
    },
  );
}

/** 根据员工获取投诉建议  GET /admin/complaints/employee/:employeeId/complaints */
export async function getByEmployee(employeeId: number) {
  return request<API.ResType<{ list?: API.Complaint[] }>>(
    `/admin/complaints/employee/${employeeId}/complaints`,
    {
      method: 'GET',
    },
  );
}

/** 查询员工建议列表  GET /admin/complaints (筛选建议类型) */
export async function employeeSuggestions(params: {
  current?: number;
  pageSize?: number;
  subCategory?: API.ComplaintSubCategory;
  status?: API.ComplaintStatus;
  keyword?: string;
  startDate?: string;
  endDate?: string;
  employeeId?: number;
  handlerId?: number;
}) {
  return request<API.ResType<{ total?: number; list?: API.Complaint[] }>>(
    '/admin/complaints',
    {
      method: 'GET',
      params: {
        ...params,
        category: 'suggestion', // 只查询建议类型
      },
    },
  );
}

/** 查询处理历史  GET /admin/complaints/:id/history */
export async function getHistory(
  id: number,
  params?: {
    page?: number;
    pageSize?: number;
  },
) {
  return request<
    API.ResType<{
      complaint: API.Complaint;
      history: {
        data: API.ComplaintHistory[];
        total: number;
        page: number;
        pageSize: number;
        totalPages: number;
      };
    }>
  >(`/admin/complaints/${id}/history`, {
    method: 'GET',
    params,
  });
}
