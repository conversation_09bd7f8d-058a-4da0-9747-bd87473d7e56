import { index as getOrders } from '@/services/order';
import type { ActionType } from '@ant-design/pro-components';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import { Modal, Typography } from 'antd';
import moment from 'moment';
import React, { useRef } from 'react';
import { formatCurrency } from '@/utils/format';

const { Text } = Typography;

interface OrderListModalProps {
  open: boolean;
  onClose: () => void;
  customerId: number;
  customerName: string;
}

const OrderListModal: React.FC<OrderListModalProps> = ({
  open,
  onClose,
  customerId,
  customerName,
}) => {
  const actionRef = useRef<ActionType>();

  const columns: ProColumns<API.Order>[] = [
    {
      title: '订单号',
      dataIndex: 'sn',
      width: 120,
      copyable: true,
    },
    {
      title: '服务项目',
      dataIndex: 'serviceName',
      width: 120,
      render: (_, record) => {
        const serviceName =
          record.orderDetails?.[0]?.service?.serviceName ||
          record.orderDetails?.[0]?.serviceName ||
          '-';
        return serviceName;
      },
    },
    {
      title: '订单金额',
      dataIndex: 'totalFee',
      width: 100,
      render: (_, record) => (
        <Text strong style={{ color: '#ff4d4f' }}>
          {formatCurrency(record.totalFee)}
        </Text>
      ),
    },
    {
      title: '订单状态',
      dataIndex: 'status',
      width: 100,
      valueEnum: {
        pending_payment: { text: '待付款', status: 'Warning' },
        pending_acceptance: { text: '待接单', status: 'Processing' },
        pending_service: { text: '待服务', status: 'Processing' },
        in_service: { text: '服务中', status: 'Processing' },
        completed: { text: '已完成', status: 'Success' },
        cancelled: { text: '已取消', status: 'Default' },
        refunded: { text: '已退款', status: 'Error' },
      },
    },
    {
      title: '服务时间',
      dataIndex: 'serviceTime',
      width: 140,
      render: (_, record) =>
        record.serviceTime
          ? moment(record.serviceTime).format('YYYY-MM-DD HH:mm')
          : '-',
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      width: 140,
      render: (_, record) =>
        record.createdAt
          ? moment(record.createdAt).format('YYYY-MM-DD HH:mm')
          : '-',
    },
  ];

  return (
    <Modal
      title={`${customerName} - 订单列表`}
      open={open}
      onCancel={onClose}
      footer={null}
      width={1000}
      destroyOnClose
    >
      <ProTable<API.Order>
        actionRef={actionRef}
        rowKey="id"
        columns={columns}
        search={false}
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
        }}
        options={false}
        request={async (params) => {
          if (!customerId) {
            return {
              data: [],
              success: true,
              total: 0,
            };
          }

          const response = await getOrders({
            ...params,
            customerId,
          });

          if (response.errCode) {
            return {
              data: [],
              success: false,
              total: 0,
            };
          }

          return {
            data: response.data?.list || [],
            success: true,
            total: response.data?.total || 0,
          };
        }}
      />
    </Modal>
  );
};

export default OrderListModal;
