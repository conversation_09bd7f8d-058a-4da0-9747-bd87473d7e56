/*
 * @Description: 宠物管理
 */
import { request } from '@umijs/max';

/** 查询宠物列表  GET /pets */
export async function index(params: Record<string, any>) {
  return request<API.ResType<{ total?: number; list?: API.Pet[] }>>(
    '/pets',
    {
      method: 'GET',
      params,
    },
  );
}

/** 创建宠物  POST /pets */
export async function create(body: Omit<API.Pet, 'id'>) {
  return request<API.ResType<API.Pet>>('/pets', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
  });
}

/** 按ID查询宠物  GET /pets/:id */
export async function show(id: number) {
  return request<API.ResType<API.Pet>>(`/pets/${id}`, {
    method: 'GET',
  });
}

/** 修改宠物  PUT /pets/:id */
export async function update(id: number, body: Partial<API.Pet>) {
  return request<API.ResType<unknown>>(`/pets/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
  });
}

/** 删除宠物  DELETE /pets/:id */
export async function remove(id: number) {
  return request<API.ResType<unknown>>(`/pets/${id}`, {
    method: 'DELETE',
  });
}

/** 根据用户ID获取宠物列表  GET /pets/customer/:customerId */
export async function getPetsByCustomer(
  customerId: number,
  params?: Record<string, any>,
) {
  return request<API.ResType<{ total?: number; list?: API.Pet[] }>>(
    `/pets/customer/${customerId}`,
    {
      method: 'GET',
      params,
    },
  );
}
