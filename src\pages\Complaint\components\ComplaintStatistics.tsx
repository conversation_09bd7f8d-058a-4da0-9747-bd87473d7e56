import { ComplaintCategory, ComplaintSubCategory } from '@/constants/complaint';
import { complaints } from '@/services';
import { ProCard, StatisticCard } from '@ant-design/pro-components';
import { Col, DatePicker, message, Row, Select, Space } from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';

const { RangePicker } = DatePicker;
const { Option } = Select;

const ComplaintStatistics: React.FC = () => {
  const [statistics, setStatistics] = useState<API.ComplaintStatistics>();
  const [loading, setLoading] = useState(false);
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs]>([
    dayjs().subtract(30, 'day'),
    dayjs(),
  ]);
  const [category, setCategory] = useState<API.ComplaintCategory | undefined>();
  const [subCategory, setSubCategory] = useState<
    API.ComplaintSubCategory | undefined
  >();

  /** 获取统计数据 */
  const fetchStatistics = async () => {
    setLoading(true);
    try {
      const response = await complaints.statistics({
        startDate: dateRange[0].format('YYYY-MM-DD'),
        endDate: dateRange[1].format('YYYY-MM-DD'),
        category,
        subCategory,
      });

      if (response.errCode) {
        message.error(response.msg || '获取统计数据失败');
      } else {
        setStatistics(response.data);
      }
    } catch (error) {
      message.error('获取统计数据失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStatistics();
  }, [dateRange, category, subCategory]);

  return (
    <div>
      {/* 筛选条件 */}
      <ProCard title="筛选条件" style={{ marginBottom: 16 }}>
        <Space wrap>
          <RangePicker
            value={dateRange}
            onChange={(dates) => {
              if (dates) {
                setDateRange([dates[0]!, dates[1]!]);
              }
            }}
            format="YYYY-MM-DD"
          />

          <Select
            placeholder="选择大类"
            style={{ width: 120 }}
            value={category}
            onChange={setCategory}
            allowClear
          >
            <Option value={ComplaintCategory.投诉}>投诉</Option>
            <Option value={ComplaintCategory.建议}>建议</Option>
          </Select>

          <Select
            placeholder="选择小类"
            style={{ width: 120 }}
            value={subCategory}
            onChange={setSubCategory}
            allowClear
          >
            <Option value={ComplaintSubCategory.订单投诉}>订单投诉</Option>
            <Option value={ComplaintSubCategory.人员投诉}>人员投诉</Option>
            <Option value={ComplaintSubCategory.平台建议}>平台建议</Option>
            <Option value={ComplaintSubCategory.服务建议}>服务建议</Option>
          </Select>
        </Space>
      </ProCard>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]}>
        <Col span={6}>
          <StatisticCard
            statistic={{
              title: '总数',
              value: statistics?.total || 0,
              precision: 0,
            }}
            loading={loading}
          />
        </Col>
        <Col span={6}>
          <StatisticCard
            statistic={{
              title: '今日新增',
              value: statistics?.todayCount || 0,
              precision: 0,
            }}
            loading={loading}
          />
        </Col>
        <Col span={6}>
          <StatisticCard
            statistic={{
              title: '本周新增',
              value: statistics?.weekCount || 0,
              precision: 0,
            }}
            loading={loading}
          />
        </Col>
        <Col span={6}>
          <StatisticCard
            statistic={{
              title: '本月新增',
              value: statistics?.monthCount || 0,
              precision: 0,
            }}
            loading={loading}
          />
        </Col>
      </Row>

      <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
        <Col span={6}>
          <StatisticCard
            statistic={{
              title: '待处理',
              value: statistics?.pendingCount || 0,
              precision: 0,
              valueStyle: { color: '#faad14' },
            }}
            loading={loading}
          />
        </Col>
        <Col span={6}>
          <StatisticCard
            statistic={{
              title: '处理中',
              value: statistics?.processingCount || 0,
              precision: 0,
              valueStyle: { color: '#1890ff' },
            }}
            loading={loading}
          />
        </Col>
        <Col span={6}>
          <StatisticCard
            statistic={{
              title: '已解决',
              value: statistics?.resolvedCount || 0,
              precision: 0,
              valueStyle: { color: '#52c41a' },
            }}
            loading={loading}
          />
        </Col>
        <Col span={6}>
          <StatisticCard
            statistic={{
              title: '已关闭',
              value: statistics?.closedCount || 0,
              precision: 0,
              valueStyle: { color: '#8c8c8c' },
            }}
            loading={loading}
          />
        </Col>
      </Row>

      {/* 分类统计 */}
      <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
        <Col span={12}>
          <ProCard title="按大类统计" loading={loading}>
            <Row gutter={16}>
              <Col span={12}>
                <StatisticCard
                  statistic={{
                    title: '投诉',
                    value: statistics?.categoryStats?.complaint || 0,
                    precision: 0,
                    valueStyle: { color: '#ff4d4f' },
                  }}
                />
              </Col>
              <Col span={12}>
                <StatisticCard
                  statistic={{
                    title: '建议',
                    value: statistics?.categoryStats?.suggestion || 0,
                    precision: 0,
                    valueStyle: { color: '#1890ff' },
                  }}
                />
              </Col>
            </Row>
          </ProCard>
        </Col>

        <Col span={12}>
          <ProCard title="按小类统计" loading={loading}>
            <Row gutter={[8, 8]}>
              <Col span={12}>
                <StatisticCard
                  statistic={{
                    title: '订单投诉',
                    value: statistics?.subCategoryStats?.order || 0,
                    precision: 0,
                  }}
                />
              </Col>
              <Col span={12}>
                <StatisticCard
                  statistic={{
                    title: '人员投诉',
                    value: statistics?.subCategoryStats?.employee || 0,
                    precision: 0,
                  }}
                />
              </Col>
              <Col span={12}>
                <StatisticCard
                  statistic={{
                    title: '平台建议',
                    value: statistics?.subCategoryStats?.platform || 0,
                    precision: 0,
                  }}
                />
              </Col>
              <Col span={12}>
                <StatisticCard
                  statistic={{
                    title: '服务建议',
                    value: statistics?.subCategoryStats?.service || 0,
                    precision: 0,
                  }}
                />
              </Col>
            </Row>
          </ProCard>
        </Col>
      </Row>

      {/* 处理效率 */}
      <Row style={{ marginTop: 16 }}>
        <Col span={24}>
          <StatisticCard
            statistic={{
              title: '平均处理时长',
              value: statistics?.avgHandleTime || 0,
              precision: 1,
              suffix: '小时',
            }}
            loading={loading}
          />
        </Col>
      </Row>
    </div>
  );
};

export default ComplaintStatistics;
