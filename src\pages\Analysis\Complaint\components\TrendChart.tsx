import * as complaints from '@/services/complaints';
import { Line } from '@ant-design/charts';
import { ProCard } from '@ant-design/pro-components';
import { message, Select } from 'antd';
import { Dayjs } from 'dayjs';
import React, { useEffect, useState } from 'react';

interface TrendChartProps {
  dateRange: [Dayjs, Dayjs];
}

const TrendChart: React.FC<TrendChartProps> = ({ dateRange }) => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<API.ComplaintTrendStats[]>([]);
  const [periodType, setPeriodType] = useState<'day' | 'week' | 'month'>('day');

  const fetchData = async () => {
    setLoading(true);
    try {
      const {
        errCode,
        msg,
        data: responseData,
      } = await complaints.trend({
        startDate: dateRange[0].format('YYYY-MM-DD'),
        endDate: dateRange[1].format('YYYY-MM-DD'),
        periodType,
      });

      if (errCode) {
        message.error(msg || '获取趋势数据失败');
        return;
      }

      if (responseData) {
        setData(responseData);
      }
    } catch (error) {
      console.error('获取趋势数据失败:', error);
      message.error('获取趋势数据失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [dateRange, periodType]);

  // 转换数据格式用于图表
  const chartData = data.flatMap((item) => [
    {
      period: item.period,
      type: '总数量',
      value: item.totalCount,
    },
    {
      period: item.period,
      type: '投诉数量',
      value: item.complaintCount,
    },
    {
      period: item.period,
      type: '建议数量',
      value: item.suggestionCount,
    },
    {
      period: item.period,
      type: '已解决数量',
      value: item.resolvedCount,
    },
  ]);

  const config = {
    data: chartData,
    xField: 'period',
    yField: 'value',
    seriesField: 'type',
    smooth: true,
    animation: {
      appear: {
        animation: 'path-in',
        duration: 1000,
      },
    },
    color: ['#1890ff', '#ff4d4f', '#52c41a', '#722ed1'],
    legend: {
      position: 'top' as const,
    },
    tooltip: {
      shared: true,
      showCrosshairs: true,
    },
  };

  return (
    <ProCard
      title="投诉建议趋势分析"
      loading={loading}
      extra={
        <Select
          value={periodType}
          onChange={setPeriodType}
          style={{ width: 100 }}
          options={[
            { label: '按天', value: 'day' },
            { label: '按周', value: 'week' },
            { label: '按月', value: 'month' },
          ]}
        />
      }
    >
      <Line {...config} height={300} />
    </ProCard>
  );
};

export default TrendChart;
