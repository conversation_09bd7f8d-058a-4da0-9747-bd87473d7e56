import {
  InfoCircleOutlined,
  QuestionCircleOutlined,
  ReloadOutlined,
} from '@ant-design/icons';
import {
  Button,
  Card,
  Col,
  Progress,
  Row,
  Statistic,
  Tooltip,
  Typography,
} from 'antd';
import React from 'react';

const { Text } = Typography;

interface StatsCardsProps {
  stats?: API.RedundantFieldStats;
  orderPriceStats?: API.OrderPriceStats;
  loading: boolean;
  onRefresh: () => void;
}

/**
 * 统计信息卡片组件
 */
const StatsCards: React.FC<StatsCardsProps> = ({
  stats,
  orderPriceStats,
  loading,
  onRefresh,
}) => {
  // 解析百分比字符串为数字
  const parsePercent = (percentStr?: string): number => {
    return parseFloat((percentStr || '0%').replace('%', ''));
  };

  // 根据百分比获取进度条状态
  const getProgressStatus = (
    percent: number,
    highThreshold = 95,
    mediumThreshold = 80,
  ) => {
    if (percent >= highThreshold) return 'success';
    if (percent >= mediumThreshold) return 'normal';
    return 'exception';
  };

  // 根据百分比获取进度条状态（反向，越低越好）
  const getReverseProgressStatus = (
    percent: number,
    lowThreshold = 1,
    mediumThreshold = 5,
  ) => {
    if (percent <= lowThreshold) return 'success';
    if (percent <= mediumThreshold) return 'normal';
    return 'exception';
  };
  return (
    <Row gutter={[16, 16]}>
      {/* 服务冗余字段统计 */}
      <Col xs={24} lg={12}>
        <Card
          title={
            <span>
              服务冗余字段统计
              <Tooltip title="显示订单详情中服务名称和价格字段的填充情况">
                <InfoCircleOutlined
                  style={{ marginLeft: 8, color: '#1890ff' }}
                />
              </Tooltip>
            </span>
          }
          extra={
            <Button
              type="text"
              icon={<ReloadOutlined />}
              loading={loading}
              onClick={onRefresh}
            >
              刷新
            </Button>
          }
        >
          {stats ? (
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <Statistic
                  title={
                    <span>
                      订单详情总记录数
                      <Tooltip title="系统中所有订单详情记录的总数量">
                        <QuestionCircleOutlined
                          style={{ marginLeft: 4, color: '#999' }}
                        />
                      </Tooltip>
                    </span>
                  }
                  value={stats.totalRecords}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title={
                    <span>
                      缺失服务名称
                      <Tooltip title="订单详情中服务名称字段为空的记录数量，这些记录需要从服务主表补充数据">
                        <QuestionCircleOutlined
                          style={{ marginLeft: 4, color: '#999' }}
                        />
                      </Tooltip>
                    </span>
                  }
                  value={stats.emptyServiceNames}
                  valueStyle={{
                    color: stats.emptyServiceNames > 0 ? '#ff4d4f' : '#52c41a',
                  }}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title={
                    <span>
                      缺失服务价格
                      <Tooltip title="订单详情中服务价格字段为空的记录数量，这些记录需要从服务主表补充数据">
                        <QuestionCircleOutlined
                          style={{ marginLeft: 4, color: '#999' }}
                        />
                      </Tooltip>
                    </span>
                  }
                  value={stats.emptyServicePrices}
                  valueStyle={{
                    color: stats.emptyServicePrices > 0 ? '#ff4d4f' : '#52c41a',
                  }}
                />
              </Col>
              <Col span={12}>
                <div>
                  <Text strong>服务名称填充率</Text>
                  <Progress
                    percent={parsePercent(stats?.serviceNameFillRate)}
                    size="small"
                    status={getProgressStatus(
                      parsePercent(stats?.serviceNameFillRate),
                    )}
                  />
                </div>
              </Col>
              <Col span={24}>
                <div>
                  <Text strong>服务价格填充率</Text>
                  <Progress
                    percent={parsePercent(stats?.servicePriceFillRate)}
                    size="small"
                    status={getProgressStatus(
                      parsePercent(stats?.servicePriceFillRate),
                    )}
                  />
                </div>
              </Col>
            </Row>
          ) : (
            <Text type="secondary">暂无数据</Text>
          )}
        </Card>
      </Col>

      {/* 订单原价统计 */}
      <Col xs={24} lg={12}>
        <Card
          title={
            <span>
              订单原价统计
              <Tooltip title="显示订单原价字段的完整性和异常情况">
                <InfoCircleOutlined
                  style={{ marginLeft: 8, color: '#1890ff' }}
                />
              </Tooltip>
            </span>
          }
        >
          {orderPriceStats ? (
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <Statistic
                  title={
                    <span>
                      订单总数
                      <Tooltip title="系统中所有订单的总数量">
                        <QuestionCircleOutlined
                          style={{ marginLeft: 4, color: '#999' }}
                        />
                      </Tooltip>
                    </span>
                  }
                  value={orderPriceStats.totalOrders}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title={
                    <span>
                      缺失原价订单
                      <Tooltip title="原价字段为0或null的订单数量，这些订单需要重新计算原价">
                        <QuestionCircleOutlined
                          style={{ marginLeft: 4, color: '#999' }}
                        />
                      </Tooltip>
                    </span>
                  }
                  value={orderPriceStats.missingOriginalPrice}
                  valueStyle={{
                    color:
                      orderPriceStats.missingOriginalPrice > 0
                        ? '#ff4d4f'
                        : '#52c41a',
                  }}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title={
                    <span>
                      有效原价订单
                      <Tooltip title="原价字段有正常值的订单数量">
                        <QuestionCircleOutlined
                          style={{ marginLeft: 4, color: '#999' }}
                        />
                      </Tooltip>
                    </span>
                  }
                  value={orderPriceStats.validOriginalPrice}
                  valueStyle={{ color: '#52c41a' }}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title={
                    <span>
                      异常价格订单
                      <Tooltip title="原价与实际计算价格差异较大的订单，可能存在数据异常或业务逻辑问题，需要人工核查">
                        <QuestionCircleOutlined
                          style={{ marginLeft: 4, color: '#999' }}
                        />
                      </Tooltip>
                    </span>
                  }
                  value={orderPriceStats.abnormalPriceOrders}
                  valueStyle={{
                    color:
                      orderPriceStats.abnormalPriceOrders > 0
                        ? '#faad14'
                        : '#52c41a',
                  }}
                />
              </Col>
              <Col span={12}>
                <div>
                  <Text strong>
                    原价填充率
                    <Tooltip title="有效原价订单占总订单的百分比，≥95%为良好，80-95%为一般，<80%需要关注">
                      <QuestionCircleOutlined
                        style={{ marginLeft: 4, color: '#999' }}
                      />
                    </Tooltip>
                  </Text>
                  <Progress
                    percent={parsePercent(
                      orderPriceStats?.originalPriceFillRate,
                    )}
                    size="small"
                    status={getProgressStatus(
                      parsePercent(orderPriceStats?.originalPriceFillRate),
                    )}
                    format={(percent) => `${percent}%`}
                  />
                </div>
              </Col>
              <Col span={12}>
                <div>
                  <Text strong>
                    异常价格率
                    <Tooltip title="价格异常订单占总订单的百分比。异常价格指：原价与根据订单详情重新计算的价格差异超过阈值的订单，可能原因：1)历史价格调整 2)促销活动 3)数据录入错误 4)业务规则变更。≤1%为良好，1-5%为一般，>5%需要核查">
                      <QuestionCircleOutlined
                        style={{ marginLeft: 4, color: '#999' }}
                      />
                    </Tooltip>
                  </Text>
                  <Progress
                    percent={parsePercent(orderPriceStats?.abnormalPriceRate)}
                    size="small"
                    status={getReverseProgressStatus(
                      parsePercent(orderPriceStats?.abnormalPriceRate),
                    )}
                    format={(percent) => `${percent}%`}
                  />
                </div>
              </Col>
            </Row>
          ) : (
            <Text type="secondary">暂无数据</Text>
          )}
        </Card>
      </Col>
    </Row>
  );
};

export default StatsCards;
