import * as order from '@/services/order';
import { ProCard } from '@ant-design/pro-components';
import { Empty, message, Select } from 'antd';
import { Dayjs } from 'dayjs';
import React, { useEffect, useState } from 'react';

interface TrendChartProps {
  dateRange: [Dayjs, Dayjs];
}

interface TrendData {
  period: string;
  orderCount: number;
  totalAmount: number;
  avgAmount: number;
}

const TrendChart: React.FC<TrendChartProps> = ({ dateRange }) => {
  const [trendData, setTrendData] = useState<TrendData[]>([]);
  const [loading, setLoading] = useState(false);
  const [groupBy, setGroupBy] = useState<'day' | 'week' | 'month'>('day');

  // 获取趋势数据
  const fetchTrendData = async () => {
    setLoading(true);
    try {
      const { errCode, msg, data } = await order.trend({
        startDate: dateRange[0].format('YYYY-MM-DD'),
        endDate: dateRange[1].format('YYYY-MM-DD'),
        groupBy,
      });

      if (errCode) {
        message.error(msg || '获取趋势数据失败');
        return;
      }

      setTrendData(data || []);
    } catch (error) {
      console.error('获取趋势数据失败:', error);
      message.error('获取趋势数据失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTrendData();
  }, [dateRange, groupBy]);

  // 简单的SVG图表实现
  const renderChart = () => {
    if (trendData.length === 0) {
      return <Empty description="暂无数据" />;
    }

    const width = 400;
    const height = 200;
    const padding = 40;
    const chartWidth = width - padding * 2;
    const chartHeight = height - padding * 2;

    const maxOrders = Math.max(...trendData.map((d) => d.orderCount));
    const maxAmount = Math.max(...trendData.map((d) => d.totalAmount));

    // 生成订单数量路径
    const ordersPath = trendData
      .map((d, i) => {
        const x = padding + (i / (trendData.length - 1)) * chartWidth;
        const y =
          padding + chartHeight - (d.orderCount / maxOrders) * chartHeight;
        return `${i === 0 ? 'M' : 'L'} ${x} ${y}`;
      })
      .join(' ');

    // 生成金额路径
    const amountPath = trendData
      .map((d, i) => {
        const x = padding + (i / (trendData.length - 1)) * chartWidth;
        const y =
          padding + chartHeight - (d.totalAmount / maxAmount) * chartHeight;
        return `${i === 0 ? 'M' : 'L'} ${x} ${y}`;
      })
      .join(' ');

    return (
      <div style={{ textAlign: 'center' }}>
        <svg
          width={width}
          height={height}
          style={{ border: '1px solid #f0f0f0' }}
        >
          {/* 网格线 */}
          <defs>
            <pattern
              id="grid"
              width="40"
              height="40"
              patternUnits="userSpaceOnUse"
            >
              <path
                d="M 40 0 L 0 0 0 40"
                fill="none"
                stroke="#f0f0f0"
                strokeWidth="1"
              />
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#grid)" />

          {/* 订单数量线 */}
          <path d={ordersPath} fill="none" stroke="#1890ff" strokeWidth="2" />

          {/* 金额线 */}
          <path d={amountPath} fill="none" stroke="#52c41a" strokeWidth="2" />

          {/* 数据点 */}
          {trendData.map((d, i) => {
            const x = padding + (i / (trendData.length - 1)) * chartWidth;
            const orderY =
              padding + chartHeight - (d.orderCount / maxOrders) * chartHeight;
            const amountY =
              padding + chartHeight - (d.totalAmount / maxAmount) * chartHeight;

            return (
              <g key={i}>
                <circle cx={x} cy={orderY} r="3" fill="#1890ff" />
                <circle cx={x} cy={amountY} r="3" fill="#52c41a" />
              </g>
            );
          })}

          {/* 图例 */}
          <g transform="translate(10, 10)">
            <circle cx="5" cy="5" r="3" fill="#1890ff" />
            <text x="15" y="9" fontSize="12" fill="#666">
              订单数量
            </text>
            <circle cx="80" cy="5" r="3" fill="#52c41a" />
            <text x="90" y="9" fontSize="12" fill="#666">
              订单金额
            </text>
          </g>
        </svg>
      </div>
    );
  };

  return (
    <ProCard
      title="订单趋势分析"
      loading={loading}
      style={{ height: '300px' }}
      extra={
        <Select
          value={groupBy}
          onChange={setGroupBy}
          style={{ width: 100 }}
          size="small"
        >
          <Select.Option value="day">按天</Select.Option>
          <Select.Option value="week">按周</Select.Option>
          <Select.Option value="month">按月</Select.Option>
        </Select>
      }
    >
      {renderChart()}
    </ProCard>
  );
};

export default TrendChart;
