import { UserOutlined } from '@ant-design/icons';
import { Avatar, Button, Descriptions, Drawer, Tag, Typography, message } from 'antd';
import moment from 'moment';
import React, { useEffect, useState } from 'react';
import { getPetCountByCustomer } from '@/services/pets';
import { index as getOrders } from '@/services/order';
import { getCardsByUser } from '@/services/customer-membership-cards';
import { getCouponsByUser } from '@/services/customer-coupons';
import OrderListModal from './OrderListModal';
import CouponListModal from './CouponListModal';

const { Title } = Typography;

interface UserDetailDrawerProps {
  open: boolean;
  onClose: () => void;
  user: API.Customer | null;
  title: string;
}

const UserDetailDrawer: React.FC<UserDetailDrawerProps> = ({
  open,
  onClose,
  user,
  title,
}) => {
  // 统计数据状态
  const [statistics, setStatistics] = useState({
    petCount: 0,
    orderCount: 0,
    totalAmount: 0,
    couponCount: 0,
    rightsCardCount: 0,
  });

  // 弹窗状态
  const [orderListVisible, setOrderListVisible] = useState(false);
  const [couponListVisible, setCouponListVisible] = useState(false);

  // 获取用户统计数据
  const fetchUserStatistics = async (userId: number) => {
    try {
      // 获取宠物数量
      const petsResponse = await getPetCountByCustomer(userId);
      const petCount = petsResponse.data?.count || 0;

      // 获取订单统计 - 通过订单列表API获取准确数据
      const ordersResponse = await getOrders({ customerId: userId, pageSize: 1 });
      const orderCount = ordersResponse.data?.total || 0;

      // 如果有订单，获取所有订单来计算总金额
      let totalAmount = 0;
      if (orderCount > 0) {
        const allOrdersResponse = await getOrders({ customerId: userId, pageSize: orderCount });
        const orders = allOrdersResponse.data?.list || [];
        totalAmount = orders.reduce((sum, order) => sum + (order.totalFee || 0), 0);
      }

      // 获取权益卡数量
      const rightsCardsResponse = await getCardsByUser(userId, { pageSize: 1 });
      const rightsCardCount = rightsCardsResponse.data?.total || 0;

      // 获取代金券数量
      const couponsResponse = await getCouponsByUser(userId, { pageSize: 1 });
      const couponCount = couponsResponse.data?.total || 0;

      setStatistics({
        petCount,
        orderCount,
        totalAmount,
        couponCount,
        rightsCardCount,
      });
    } catch (error) {
      console.error('获取用户统计数据失败:', error);
      message.error('获取统计数据失败');
    }
  };

  // 当用户信息变化时获取统计数据
  useEffect(() => {
    if (user?.id && open) {
      fetchUserStatistics(user.id);
    }
  }, [user?.id, open]);
  if (!user) {
    return (
      <Drawer
        title={title}
        placement="right"
        onClose={onClose}
        open={open}
        width={600}
      >
        <div style={{ textAlign: 'center', padding: '50px 0' }}>
          <Typography.Text type="secondary">暂无用户信息</Typography.Text>
        </div>
      </Drawer>
    );
  }

  return (
    <Drawer
      title={title}
      placement="right"
      onClose={onClose}
      open={open}
      width={600}
    >
      <div style={{ marginBottom: 24, textAlign: 'center' }}>
        <Avatar
          size={80}
          src={user.avatar}
          icon={<UserOutlined />}
          style={{ marginBottom: 16 }}
        />
        <Title level={4} style={{ margin: 0 }}>
          {user.nickname || '未知用户'}
        </Title>
      </div>

      <Descriptions
        title="基本信息"
        bordered
        column={1}
        size="small"
        style={{ marginBottom: 24 }}
      >
        <Descriptions.Item label="用户ID">{user.id}</Descriptions.Item>
        <Descriptions.Item label="昵称">
          {user.nickname || '-'}
        </Descriptions.Item>
        <Descriptions.Item label="手机号">
          {user.phone || '-'}
        </Descriptions.Item>
        <Descriptions.Item label="性别">
          {user.gender === 1 ? '男' : user.gender === 0 ? '女' : '保密'}
        </Descriptions.Item>
        <Descriptions.Item label="会员状态">
          <Tag color={user.memberStatus === 1 ? 'gold' : 'default'}>
            {user.memberStatus === 1 ? '会员' : '非会员'}
          </Tag>
        </Descriptions.Item>
        <Descriptions.Item label="积分">
          <Tag color="blue">{user.points || 0} 分</Tag>
        </Descriptions.Item>
        <Descriptions.Item label="最后登录">
          {user.lastLoginTime
            ? moment(user.lastLoginTime).format('YYYY-MM-DD HH:mm:ss')
            : '-'}
        </Descriptions.Item>
      </Descriptions>

      <Descriptions
        title="地址信息"
        bordered
        column={1}
        size="small"
        style={{ marginBottom: 24 }}
      >
        <Descriptions.Item label="详细地址">
          {user.address || '-'}
        </Descriptions.Item>
      </Descriptions>

      <Descriptions title="统计信息" bordered column={1} size="small">
        <Descriptions.Item label="宠物数量">
          <Tag color="blue">{statistics.petCount} 只</Tag>
        </Descriptions.Item>
        <Descriptions.Item label="订单数量">
          <Button
            type="link"
            size="small"
            style={{ padding: 0, height: 'auto' }}
            onClick={() => setOrderListVisible(true)}
          >
            <Tag color="green">{statistics.orderCount} 单</Tag>
          </Button>
        </Descriptions.Item>
        <Descriptions.Item label="订单总金额">
          <Button
            type="link"
            size="small"
            style={{ padding: 0, height: 'auto' }}
            onClick={() => setOrderListVisible(true)}
          >
            <Tag color="orange">¥{statistics.totalAmount.toFixed(2)}</Tag>
          </Button>
        </Descriptions.Item>
        <Descriptions.Item label="权益卡数量">
          <Button
            type="link"
            size="small"
            style={{ padding: 0, height: 'auto' }}
            onClick={() => setCouponListVisible(true)}
          >
            <Tag color="purple">{statistics.rightsCardCount} 张</Tag>
          </Button>
        </Descriptions.Item>
        <Descriptions.Item label="代金券数量">
          <Button
            type="link"
            size="small"
            style={{ padding: 0, height: 'auto' }}
            onClick={() => setCouponListVisible(true)}
          >
            <Tag color="cyan">{statistics.couponCount} 张</Tag>
          </Button>
        </Descriptions.Item>
        <Descriptions.Item label="账户状态">
          <Tag color={user.status === 1 ? 'success' : 'error'}>
            {user.status === 1 ? '正常' : '禁用'}
          </Tag>
        </Descriptions.Item>
      </Descriptions>

      {/* 订单列表弹窗 */}
      <OrderListModal
        open={orderListVisible}
        onClose={() => setOrderListVisible(false)}
        customerId={user?.id || 0}
        customerName={user?.nickname || '未知用户'}
      />

      {/* 卡券列表弹窗 */}
      <CouponListModal
        open={couponListVisible}
        onClose={() => setCouponListVisible(false)}
        customerId={user?.id || 0}
        customerName={user?.nickname || '未知用户'}
      />
    </Drawer>
  );
};

export default UserDetailDrawer;
