import * as complaints from '@/services/complaints';
import { Pie } from '@ant-design/charts';
import { ProCard } from '@ant-design/pro-components';
import { message, Select, Space } from 'antd';
import { Dayjs } from 'dayjs';
import React, { useEffect, useState } from 'react';

interface StatusDistributionProps {
  dateRange: [Dayjs, Dayjs];
}

const StatusDistribution: React.FC<StatusDistributionProps> = ({
  dateRange,
}) => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<API.ComplaintStatusDistributionStats>({
    statusStats: [],
    categoryStats: [],
    subCategoryStats: [],
  });
  const [viewType, setViewType] = useState<
    'status' | 'category' | 'subCategory'
  >('status');

  const fetchData = async () => {
    setLoading(true);
    try {
      const {
        errCode,
        msg,
        data: responseData,
      } = await complaints.statusDistribution({
        startDate: dateRange[0].format('YYYY-MM-DD'),
        endDate: dateRange[1].format('YYYY-MM-DD'),
      });

      if (errCode) {
        message.error(msg || '获取状态分布数据失败');
        return;
      }

      if (responseData) {
        setData(responseData);
      }
    } catch (error) {
      console.error('获取状态分布数据失败:', error);
      message.error('获取状态分布数据失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [dateRange]);

  // 状态标签映射
  const getStatusLabel = (status: string) => {
    const statusMap: Record<string, string> = {
      pending: '待处理',
      processing: '处理中',
      resolved: '已解决',
      closed: '已关闭',
    };
    return statusMap[status] || status;
  };

  // 分类标签映射
  const getCategoryLabel = (category: string) => {
    const categoryMap: Record<string, string> = {
      complaint: '投诉',
      suggestion: '建议',
    };
    return categoryMap[category] || category;
  };

  // 子分类标签映射
  const getSubCategoryLabel = (subCategory: string) => {
    const subCategoryMap: Record<string, string> = {
      order: '订单投诉',
      employee: '人员投诉',
      platform: '平台建议',
      service: '服务建议',
    };
    return subCategoryMap[subCategory] || subCategory;
  };

  // 获取当前视图的数据
  const getCurrentData = () => {
    switch (viewType) {
      case 'status':
        return data.statusStats.map((item) => ({
          type: getStatusLabel(item.status),
          value: item.count,
          percentage: item.percentage,
        }));
      case 'category':
        return data.categoryStats.map((item) => ({
          type: getCategoryLabel(item.category),
          value: item.count,
          percentage: item.percentage,
        }));
      case 'subCategory':
        return data.subCategoryStats.map((item) => ({
          type: getSubCategoryLabel(item.subCategory),
          value: item.count,
          percentage: item.percentage,
        }));
      default:
        return [];
    }
  };

  const config = {
    data: getCurrentData(),
    angleField: 'value',
    colorField: 'type',
    radius: 0.8,
    label: {
      type: 'outer',
      content: '{name} {percentage}',
    },
    interactions: [
      {
        type: 'element-active',
      },
    ],
    legend: {
      position: 'bottom' as const,
    },
    tooltip: {
      formatter: (datum: any) => {
        return {
          name: datum.type,
          value: `${datum.value} (${datum.percentage.toFixed(1)}%)`,
        };
      },
    },
  };

  console.log('config: ', config);
  return (
    <ProCard
      title="分布统计"
      loading={loading}
      extra={
        <Space>
          <Select
            value={viewType}
            onChange={setViewType}
            style={{ width: 120 }}
            options={[
              { label: '状态分布', value: 'status' },
              { label: '类型分布', value: 'category' },
              { label: '子类分布', value: 'subCategory' },
            ]}
          />
        </Space>
      }
    >
      <Pie {...config} height={300} />
    </ProCard>
  );
};

export default StatusDistribution;
