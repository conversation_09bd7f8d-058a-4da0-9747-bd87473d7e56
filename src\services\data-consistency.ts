/*
 * @Description: 数据一致性维护服务
 */
import { request } from '@umijs/max';

/** 检查数据一致性  GET /admin/data-consistency/check */
export async function checkConsistency() {
  return request<API.ResType<API.DataConsistencyCheckResult>>(
    '/admin/data-consistency/check',
    {
      method: 'GET',
    },
  );
}

/** 获取冗余字段统计信息  GET /admin/data-consistency/stats */
export async function getStats() {
  return request<API.ResType<API.RedundantFieldStats>>(
    '/admin/data-consistency/stats',
    {
      method: 'GET',
    },
  );
}

/** 修复数据不一致  POST /admin/data-consistency/repair */
export async function repairInconsistency(params?: {
  method?: 'auto' | 'batch';
}) {
  return request<API.ResType<API.RepairResult>>(
    '/admin/data-consistency/repair',
    {
      method: 'POST',
      params,
    },
  );
}

/** 修复缺失的冗余字段  POST /admin/data-consistency/repair-missing */
export async function repairMissingFields() {
  return request<API.ResType<number>>(
    '/admin/data-consistency/repair-missing',
    {
      method: 'POST',
    },
  );
}

/** 同步特定服务信息  POST /admin/data-consistency/sync-service */
export async function syncService(params: {
  serviceId: number;
  serviceName?: string;
  basePrice?: number;
}) {
  return request<API.ResType<number>>('/admin/data-consistency/sync-service', {
    method: 'POST',
    params,
  });
}

/** 检查缺失原价的订单  GET /admin/data-consistency/check-original-price */
export async function checkOriginalPrice() {
  return request<API.ResType<API.OrderPriceCheckResult>>(
    '/admin/data-consistency/check-original-price',
    {
      method: 'GET',
    },
  );
}

/** 获取订单原价统计信息  GET /admin/data-consistency/order-price-stats */
export async function getOrderPriceStats() {
  return request<API.ResType<API.OrderPriceStats>>(
    '/admin/data-consistency/order-price-stats',
    {
      method: 'GET',
    },
  );
}

/** 补充订单原价  POST /admin/data-consistency/repair-original-price */
export async function repairOriginalPrice(params?: {
  method?: 'auto' | 'batch';
}) {
  return request<API.ResType<API.RepairResult>>(
    '/admin/data-consistency/repair-original-price',
    {
      method: 'POST',
      params,
    },
  );
}

/** 执行完整维护任务  POST /admin/data-consistency/maintenance */
export async function performMaintenance() {
  return request<API.ResType<API.MaintenanceTaskResult>>(
    '/admin/data-consistency/maintenance',
    {
      method: 'POST',
    },
  );
}

/** 验证修复结果  GET /admin/data-consistency/validate */
export async function validateRepair() {
  return request<API.ResType<API.ValidationResult>>(
    '/admin/data-consistency/validate',
    {
      method: 'GET',
    },
  );
}

// ==================== 异常价格检测相关接口 ====================

/** 检查异常价格订单  GET /admin/data-consistency/check-abnormal-price */
export async function checkAbnormalPrice(params?: {
  threshold?: number;
  limit?: number;
}) {
  return request<API.ResType<API.AbnormalPriceCheckResult>>(
    '/admin/data-consistency/check-abnormal-price',
    {
      method: 'GET',
      params,
    },
  );
}

/** 获取异常价格详情  GET /admin/data-consistency/abnormal-price-detail/:orderId */
export async function getAbnormalPriceDetail(orderId: number) {
  return request<API.ResType<API.AbnormalPriceDetail>>(
    `/admin/data-consistency/abnormal-price-detail/${orderId}`,
    {
      method: 'GET',
    },
  );
}

/** 批量修正异常价格  POST /admin/data-consistency/batch-fix-abnormal-price */
export async function batchFixAbnormalPrice(params: {
  orderIds: string;
  fixType?: 'recalculate' | 'adjust';
}) {
  return request<API.ResType<API.AbnormalPriceFixResult>>(
    '/admin/data-consistency/batch-fix-abnormal-price',
    {
      method: 'POST',
      params,
    },
  );
}

/** 设置异常价格阈值  POST /admin/data-consistency/set-abnormal-threshold */
export async function setAbnormalThreshold(params: { threshold: number }) {
  return request<API.ResType<API.AbnormalPriceThresholdResult>>(
    '/admin/data-consistency/set-abnormal-threshold',
    {
      method: 'POST',
      params,
    },
  );
}

/** 获取异常价格统计报告  GET /admin/data-consistency/abnormal-price-report */
export async function getAbnormalPriceReport(params?: { threshold?: number }) {
  return request<API.ResType<API.AbnormalPriceReport>>(
    '/admin/data-consistency/abnormal-price-report',
    {
      method: 'GET',
      params,
    },
  );
}
