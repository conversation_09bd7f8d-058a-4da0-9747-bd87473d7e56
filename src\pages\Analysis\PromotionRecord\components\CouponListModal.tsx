import { Modal, Tabs, Tag, Typography } from 'antd';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import React, { useRef } from 'react';
import type { ActionType } from '@ant-design/pro-components';
import { getCardsByUser } from '@/services/customer-membership-cards';
import { getCouponsByUser } from '@/services/customer-coupons';
import moment from 'moment';

const { Text } = Typography;
const { TabPane } = Tabs;

interface CouponListModalProps {
  open: boolean;
  onClose: () => void;
  customerId: number;
  customerName: string;
}

const CouponListModal: React.FC<CouponListModalProps> = ({
  open,
  onClose,
  customerId,
  customerName,
}) => {
  const rightsCardActionRef = useRef<ActionType>();
  const couponActionRef = useRef<ActionType>();

  // 权益卡列表列配置
  const rightsCardColumns: ProColumns<API.CustomerMembershipCard>[] = [
    {
      title: '卡片名称',
      dataIndex: 'cardName',
      width: 150,
      render: (_, record) => record.membershipCardType?.name || '-',
    },
    {
      title: '卡片类型',
      dataIndex: 'cardType',
      width: 100,
      render: (_, record) => record.membershipCardType?.type || '-',
    },
    {
      title: '剩余次数',
      dataIndex: 'remainingCount',
      width: 100,
      render: (_, record) => (
        <Tag color={record.remainingCount > 0 ? 'green' : 'red'}>
          {record.remainingCount || 0}
        </Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 80,
      valueEnum: {
        active: { text: '有效', status: 'Success' },
        expired: { text: '已过期', status: 'Error' },
        used_up: { text: '已用完', status: 'Default' },
      },
    },
    {
      title: '有效期',
      dataIndex: 'expiryDate',
      width: 120,
      render: (_, record) => {
        if (!record.expiryDate) return '无限期';
        const isExpired = moment(record.expiryDate).isBefore(moment());
        return (
          <Text type={isExpired ? 'danger' : 'default'}>
            {moment(record.expiryDate).format('YYYY-MM-DD')}
          </Text>
        );
      },
    },
    {
      title: '获得时间',
      dataIndex: 'createdAt',
      width: 140,
      render: (_, record) =>
        record.createdAt
          ? moment(record.createdAt).format('YYYY-MM-DD HH:mm')
          : '-',
    },
  ];

  // 代金券列表列配置
  const couponColumns: ProColumns<API.CustomerCoupon>[] = [
    {
      title: '券名称',
      dataIndex: 'couponName',
      width: 150,
      render: (_, record) => record.coupon?.name || '-',
    },
    {
      title: '券类型',
      dataIndex: 'couponType',
      width: 100,
      render: (_, record) => record.coupon?.type || '-',
    },
    {
      title: '面额',
      dataIndex: 'amount',
      width: 100,
      render: (_, record) => (
        <Text strong style={{ color: '#ff4d4f' }}>
          ¥{record.coupon?.amount?.toFixed(2) || '0.00'}
        </Text>
      ),
    },
    {
      title: '使用门槛',
      dataIndex: 'minAmount',
      width: 100,
      render: (_, record) => {
        const minAmount = record.coupon?.minAmount;
        return minAmount ? `满¥${minAmount.toFixed(2)}可用` : '无门槛';
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 80,
      valueEnum: {
        unused: { text: '未使用', status: 'Success' },
        used: { text: '已使用', status: 'Default' },
        expired: { text: '已过期', status: 'Error' },
      },
    },
    {
      title: '有效期',
      dataIndex: 'expiryDate',
      width: 120,
      render: (_, record) => {
        if (!record.expiryDate) return '无限期';
        const isExpired = moment(record.expiryDate).isBefore(moment());
        return (
          <Text type={isExpired ? 'danger' : 'default'}>
            {moment(record.expiryDate).format('YYYY-MM-DD')}
          </Text>
        );
      },
    },
    {
      title: '获得时间',
      dataIndex: 'createdAt',
      width: 140,
      render: (_, record) =>
        record.createdAt
          ? moment(record.createdAt).format('YYYY-MM-DD HH:mm')
          : '-',
    },
  ];

  return (
    <Modal
      title={`${customerName} - 卡券列表`}
      open={open}
      onCancel={onClose}
      footer={null}
      width={1200}
      destroyOnClose
    >
      <Tabs defaultActiveKey="rightsCards">
        <TabPane tab="权益卡" key="rightsCards">
          <ProTable<API.CustomerMembershipCard>
            actionRef={rightsCardActionRef}
            rowKey="id"
            columns={rightsCardColumns}
            search={false}
            pagination={{
              pageSize: 10,
              showSizeChanger: true,
            }}
            options={false}
            request={async (params) => {
              if (!customerId) {
                return {
                  data: [],
                  success: true,
                  total: 0,
                };
              }

              const response = await getCardsByUser(customerId, params);

              if (response.errCode) {
                return {
                  data: [],
                  success: false,
                  total: 0,
                };
              }

              return {
                data: response.data?.list || [],
                success: true,
                total: response.data?.total || 0,
              };
            }}
          />
        </TabPane>
        <TabPane tab="代金券" key="coupons">
          <ProTable<API.CustomerCoupon>
            actionRef={couponActionRef}
            rowKey="id"
            columns={couponColumns}
            search={false}
            pagination={{
              pageSize: 10,
              showSizeChanger: true,
            }}
            options={false}
            request={async (params) => {
              if (!customerId) {
                return {
                  data: [],
                  success: true,
                  total: 0,
                };
              }

              const response = await getCouponsByUser(customerId, params);

              if (response.errCode) {
                return {
                  data: [],
                  success: false,
                  total: 0,
                };
              }

              return {
                data: response.data?.list || [],
                success: true,
                total: response.data?.total || 0,
              };
            }}
          />
        </TabPane>
      </Tabs>
    </Modal>
  );
};

export default CouponListModal;
