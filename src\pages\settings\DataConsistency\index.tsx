import {
  checkConsistency,
  checkOriginalPrice,
  getOrderPriceStats,
  getStats,
  performMaintenance,
  repairInconsistency,
  repairMissingFields,
  repairOriginalPrice,
  validateRepair,
} from '@/services/data-consistency';
import { PageContainer } from '@ant-design/pro-components';
import { Alert, Card, Col, Row, Steps, Tabs, Typography, message } from 'antd';
import React, { useEffect, useState } from 'react';
import AbnormalPriceManager from './AbnormalPriceManager';
import './index.less';
import OperationPanel from './OperationPanel';
import ResultDisplay from './ResultDisplay';
import StatsCards from './StatsCards';

const { Paragraph } = Typography;

/**
 * 数据一致性维护主页面
 */
const DataConsistency: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [stats, setStats] = useState<API.RedundantFieldStats>();
  const [orderPriceStats, setOrderPriceStats] = useState<API.OrderPriceStats>();
  const [consistencyResult, setConsistencyResult] =
    useState<API.DataConsistencyCheckResult>();
  const [orderPriceResult, setOrderPriceResult] =
    useState<API.OrderPriceCheckResult>();
  const [repairResults, setRepairResults] = useState<{
    consistency?: API.RepairResult;
    missingFields?: API.RepairResult;
    originalPrice?: API.RepairResult;
    maintenance?: API.MaintenanceTaskResult;
    validation?: API.ValidationResult;
  }>({});

  // 加载统计信息
  const loadStats = async () => {
    try {
      const [statsRes, orderStatsRes] = await Promise.all([
        getStats(),
        getOrderPriceStats(),
      ]);

      if (!statsRes.errCode) {
        setStats(statsRes.data);
      } else {
        message.error(statsRes.msg || '获取冗余字段统计失败');
      }

      if (!orderStatsRes.errCode) {
        setOrderPriceStats(orderStatsRes.data);
      } else {
        message.error(orderStatsRes.msg || '获取订单原价统计失败');
      }
    } catch (error) {
      message.error('加载统计信息失败');
    }
  };

  // 检查数据一致性
  const handleCheckConsistency = async () => {
    setLoading(true);
    try {
      const res = await checkConsistency();
      if (!res.errCode) {
        setConsistencyResult(res.data);
        message.success(res.msg || '检查完成');
      } else {
        message.error(res.msg || '数据一致性检查失败');
      }
    } catch (error) {
      message.error('数据一致性检查失败');
    } finally {
      setLoading(false);
    }
  };

  // 检查订单原价
  const handleCheckOriginalPrice = async () => {
    setLoading(true);
    try {
      const res = await checkOriginalPrice();
      if (!res.errCode) {
        setOrderPriceResult(res.data);
        message.success(res.msg || '检查完成');
      } else {
        message.error(res.msg || '订单原价检查失败');
      }
    } catch (error) {
      message.error('订单原价检查失败');
    } finally {
      setLoading(false);
    }
  };

  // 修复数据不一致
  const handleRepairConsistency = async (method: 'auto' | 'batch' = 'auto') => {
    setLoading(true);
    try {
      const res = await repairInconsistency({ method });
      if (!res.errCode) {
        setRepairResults((prev) => ({ ...prev, consistency: res.data }));
        message.success(res.msg || '修复完成');
        // 重新加载统计信息和检查结果
        await loadStats();
        await handleCheckConsistency();
      } else {
        message.error(res.msg || '修复失败');
      }
    } catch (error) {
      message.error('修复失败');
    } finally {
      setLoading(false);
    }
  };

  // 修复缺失字段
  const handleRepairMissingFields = async () => {
    setLoading(true);
    try {
      const res = await repairMissingFields();
      if (!res.errCode) {
        // API返回的是数字，需要转换为RepairResult格式
        const repairResult: API.RepairResult = {
          repairedCount: res.data || 0,
          message: `成功修复${res.data || 0}条缺失记录`,
        };
        setRepairResults((prev) => ({ ...prev, missingFields: repairResult }));
        message.success(res.msg || '修复完成');
        await loadStats();
      } else {
        message.error(res.msg || '修复失败');
      }
    } catch (error) {
      message.error('修复失败');
    } finally {
      setLoading(false);
    }
  };

  // 修复订单原价
  const handleRepairOriginalPrice = async (
    method: 'auto' | 'batch' = 'batch',
  ) => {
    setLoading(true);
    try {
      const res = await repairOriginalPrice({ method });
      if (!res.errCode) {
        setRepairResults((prev) => ({ ...prev, originalPrice: res.data }));
        message.success(res.msg || '修复完成');
        await loadStats();
        await handleCheckOriginalPrice();
      } else {
        message.error(res.msg || '修复失败');
      }
    } catch (error) {
      message.error('修复失败');
    } finally {
      setLoading(false);
    }
  };

  // 执行完整维护
  const handlePerformMaintenance = async () => {
    setLoading(true);
    try {
      const res = await performMaintenance();
      if (!res.errCode) {
        setRepairResults((prev) => ({ ...prev, maintenance: res.data }));
        message.success(res.msg || '维护任务执行完成');
        await loadStats();
      } else {
        message.error(res.msg || '维护任务执行失败');
      }
    } catch (error) {
      message.error('维护任务执行失败');
    } finally {
      setLoading(false);
    }
  };

  // 验证修复结果
  const handleValidateRepair = async () => {
    setLoading(true);
    try {
      const res = await validateRepair();
      if (!res.errCode) {
        setRepairResults((prev) => ({ ...prev, validation: res.data }));
        message.success(res.msg || '验证完成');
      } else {
        message.error(res.msg || '验证失败');
      }
    } catch (error) {
      message.error('验证失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadStats();
  }, []);

  return (
    <PageContainer
      title="数据一致性维护"
      className="data-consistency"
      content={
        <div>
          <Paragraph>
            数据一致性维护工具用于检查和修复系统中的数据一致性问题，确保订单数据的准确性和完整性。
          </Paragraph>
          <Alert
            message="功能说明"
            description={
              <div>
                <div>
                  <strong>服务冗余字段维护：</strong>
                  检查并修复订单详情中服务名称、价格等字段与服务主表不一致的问题
                </div>
                <div>
                  <strong>订单原价补充：</strong>
                  为缺失原价的订单重新计算并补充原价信息
                </div>
                <div>
                  <strong>异常价格检测：</strong>
                  识别价格异常的订单，可能由历史价格调整、促销活动或数据错误引起
                </div>
              </div>
            }
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />
          <Alert
            message="操作建议"
            description="建议按照以下步骤进行操作：查看统计信息了解数据状况 → 执行检查发现具体问题 → 进行修复操作解决问题 → 验证修复结果确认效果"
            type="success"
            showIcon
            style={{ marginBottom: 16 }}
          />
        </div>
      }
    >
      <Tabs
        defaultActiveKey="maintenance"
        items={[
          {
            key: 'maintenance',
            label: '数据维护',
            children: (
              <Row gutter={[16, 16]}>
                {/* 操作步骤指引 */}
                <Col span={24}>
                  <Card title="操作步骤指引" size="small">
                    <Steps
                      size="small"
                      items={[
                        {
                          title: '查看统计',
                          description: '了解当前数据状况',
                        },
                        {
                          title: '执行检查',
                          description: '发现具体问题',
                        },
                        {
                          title: '修复数据',
                          description: '解决发现的问题',
                        },
                        {
                          title: '验证结果',
                          description: '确认修复效果',
                        },
                      ]}
                    />
                  </Card>
                </Col>

                {/* 统计信息卡片 */}
                <Col span={24}>
                  <StatsCards
                    stats={stats}
                    orderPriceStats={orderPriceStats}
                    loading={loading}
                    onRefresh={loadStats}
                  />
                </Col>

                {/* 操作面板 */}
                <Col span={24}>
                  <Card title="维护操作" className="operation-panel">
                    <OperationPanel
                      loading={loading}
                      onCheckConsistency={handleCheckConsistency}
                      onCheckOriginalPrice={handleCheckOriginalPrice}
                      onRepairConsistency={handleRepairConsistency}
                      onRepairMissingFields={handleRepairMissingFields}
                      onRepairOriginalPrice={handleRepairOriginalPrice}
                      onPerformMaintenance={handlePerformMaintenance}
                      onValidateRepair={handleValidateRepair}
                    />
                  </Card>
                </Col>

                {/* 结果展示 */}
                <Col span={24}>
                  <div className="result-display">
                    <ResultDisplay
                      consistencyResult={consistencyResult}
                      orderPriceResult={orderPriceResult}
                      repairResults={repairResults}
                    />
                  </div>
                </Col>
              </Row>
            ),
          },
          {
            key: 'abnormal-price',
            label: '异常价格管理',
            children: <AbnormalPriceManager onRefreshStats={loadStats} />,
          },
        ]}
      />
    </PageContainer>
  );
};

export default DataConsistency;
