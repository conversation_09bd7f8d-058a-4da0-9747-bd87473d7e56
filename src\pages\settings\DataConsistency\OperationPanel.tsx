import {
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined,
  PlayCircleOutlined,
  ReloadOutlined,
  ToolOutlined,
} from '@ant-design/icons';
import {
  Button,
  Col,
  Divider,
  Modal,
  Row,
  Space,
  Tooltip,
  Typography,
} from 'antd';
import React from 'react';

const { Title, Text } = Typography;

interface OperationPanelProps {
  loading: boolean;
  onCheckConsistency: () => void;
  onCheckOriginalPrice: () => void;
  onRepairConsistency: (method: 'auto' | 'batch') => void;
  onRepairMissingFields: () => void;
  onRepairOriginalPrice: (method: 'auto' | 'batch') => void;
  onPerformMaintenance: () => void;
  onValidateRepair: () => void;
}

/**
 * 操作面板组件
 */
const OperationPanel: React.FC<OperationPanelProps> = ({
  loading,
  onCheckConsistency,
  onCheckOriginalPrice,
  onRepairConsistency,
  onRepairMissingFields,
  onRepairOriginalPrice,
  onPerformMaintenance,
  onValidateRepair,
}) => {
  // 确认修复操作
  const confirmRepair = (
    title: string,
    content: string,
    onConfirm: () => void,
    danger = false,
  ) => {
    Modal.confirm({
      title,
      content,
      icon: danger ? <ExclamationCircleOutlined /> : <CheckCircleOutlined />,
      okText: '确认',
      cancelText: '取消',
      onOk: onConfirm,
      okButtonProps: { danger },
    });
  };

  // 确认批量修复
  const confirmBatchRepair = (
    title: string,
    content: string,
    onConfirm: (method: 'auto' | 'batch') => void,
  ) => {
    Modal.confirm({
      title,
      content: (
        <div>
          <Text>{content}</Text>
          <br />
          <br />
          <Text type="secondary">
            • 逐条修复：适用于小数据量（&lt; 1000条），安全性高
            <br />• 批量修复：适用于大数据量（&gt; 1000条），效率高
          </Text>
        </div>
      ),
      okText: '批量修复',
      cancelText: '逐条修复',
      onOk: () => onConfirm('batch'),
      onCancel: () => onConfirm('auto'),
    });
  };

  return (
    <div>
      {/* 检查操作 */}
      <Title level={5}>
        <InfoCircleOutlined style={{ marginRight: 8, color: '#1890ff' }} />
        数据检查
      </Title>
      <Text type="secondary" style={{ display: 'block', marginBottom: 16 }}>
        检查系统中的数据一致性问题，了解当前数据状况。建议在执行修复操作前先进行检查。
      </Text>
      <Row gutter={[16, 16]}>
        <Col>
          <Tooltip title="检查订单详情中服务冗余字段与主表数据的一致性">
            <Button
              type="primary"
              icon={<CheckCircleOutlined />}
              loading={loading}
              onClick={onCheckConsistency}
            >
              检查数据一致性
            </Button>
          </Tooltip>
        </Col>
        <Col>
          <Tooltip title="检查系统中原价为0或null的订单">
            <Button
              type="primary"
              icon={<CheckCircleOutlined />}
              loading={loading}
              onClick={onCheckOriginalPrice}
            >
              检查订单原价
            </Button>
          </Tooltip>
        </Col>
      </Row>

      <Divider />

      {/* 修复操作 */}
      <Title level={5}>
        <ToolOutlined style={{ marginRight: 8, color: '#52c41a' }} />
        数据修复
      </Title>
      <Text type="secondary" style={{ display: 'block', marginBottom: 16 }}>
        根据检查结果修复发现的数据问题。<strong>逐条修复</strong>
        适用于小数据量（&lt;1000条），安全性高；<strong>批量修复</strong>
        适用于大数据量（&gt;1000条），效率高。
      </Text>
      <Row gutter={[16, 16]}>
        <Col>
          <Tooltip title="修复订单详情中与服务主表不一致的冗余字段数据">
            <Button
              type="default"
              icon={<ToolOutlined />}
              loading={loading}
              onClick={() =>
                confirmBatchRepair(
                  '修复数据不一致',
                  '此操作将修复订单详情中与服务主表不一致的冗余字段数据，请选择修复方式：',
                  onRepairConsistency,
                )
              }
            >
              修复数据不一致
            </Button>
          </Tooltip>
        </Col>
        <Col>
          <Tooltip title="为订单详情中缺失的服务名称和价格字段补充数据">
            <Button
              type="default"
              icon={<ToolOutlined />}
              loading={loading}
              onClick={() =>
                confirmRepair(
                  '修复缺失字段',
                  '此操作将为订单详情中缺失的服务名称和价格字段补充数据。',
                  onRepairMissingFields,
                )
              }
            >
              修复缺失字段
            </Button>
          </Tooltip>
        </Col>
        <Col>
          <Tooltip title="为缺失原价的订单自动计算并补充原价信息">
            <Button
              type="default"
              icon={<ToolOutlined />}
              loading={loading}
              onClick={() =>
                confirmBatchRepair(
                  '补充订单原价',
                  '此操作将为缺失原价的订单自动计算并补充原价信息，请选择修复方式：',
                  onRepairOriginalPrice,
                )
              }
            >
              补充订单原价
            </Button>
          </Tooltip>
        </Col>
      </Row>

      <Divider />

      {/* 综合操作 */}
      <Title level={5}>
        <PlayCircleOutlined style={{ marginRight: 8, color: '#fa8c16' }} />
        综合维护
      </Title>
      <Text type="secondary" style={{ display: 'block', marginBottom: 16 }}>
        <strong>执行完整维护</strong>
        ：一键执行检查、修复、统计等所有步骤，建议在业务低峰期使用；
        <strong>验证修复结果</strong>：确认修复操作的效果。
      </Text>
      <Space>
        <Tooltip title="执行完整的数据一致性维护任务，包括检查、修复、统计等所有步骤">
          <Button
            type="primary"
            size="large"
            icon={<PlayCircleOutlined />}
            loading={loading}
            onClick={() =>
              confirmRepair(
                '执行完整维护任务',
                '此操作将执行完整的数据一致性维护任务，包括检查、修复、统计等所有步骤。建议在低峰期执行。',
                onPerformMaintenance,
                true,
              )
            }
          >
            执行完整维护
          </Button>
        </Tooltip>
        <Tooltip title="验证数据修复操作的结果，确认数据一致性状态">
          <Button
            icon={<ReloadOutlined />}
            loading={loading}
            onClick={() =>
              confirmRepair(
                '验证修复结果',
                '此操作将验证数据修复操作的结果，确认数据一致性状态。',
                onValidateRepair,
              )
            }
          >
            验证修复结果
          </Button>
        </Tooltip>
      </Space>

      <div
        style={{
          marginTop: 16,
          padding: 12,
          background: '#f6f8fa',
          borderRadius: 6,
        }}
      >
        <Text type="secondary">
          <strong>💡 重要说明：</strong>
          <br />• <strong>异常价格率</strong>
          ：即使处理了所有缺失原价，仍可能存在异常价格订单。这些订单的原价与重新计算的价格存在差异，可能由历史价格调整、促销活动或数据录入错误引起
          <br />• <strong>数据安全</strong>
          ：所有修复操作都有确认对话框，建议在业务低峰期执行大批量操作
          <br />• <strong>操作建议</strong>
          ：如果异常价格率较高（&gt;5%），建议先检查具体的异常订单，确认是否需要人工处理
        </Text>
      </div>
    </div>
  );
};

export default OperationPanel;
